# Real-Time Chat System - Frontend Integration Guide

A production-ready real-time chat system built with NestJS, Socket.IO, and TypeORM.

## 🚀 Quick Start

```javascript
import io from 'socket.io-client';

const token = 'your-jwt-token';
const socket = io('http://localhost:3000/chat', {
  auth: { token },
  withCredentials: true
});

// User is automatically joined to all their chat rooms
socket.on('connected', (data) => {
  console.log('Connected to chat system:', data);
});

// Handle connection errors (including CORS issues)
socket.on('connect_error', (error) => {
  console.error('Connection failed:', error.message);
});
```

### CORS Configuration

The server is configured to accept connections from common development ports:
- `http://localhost:3000` (NestJS default)
- `http://localhost:3001` (React/Next.js alternative)
- `http://localhost:5173` (Vite default)
- `http://localhost:5174` (Vite alternative)
- `http://localhost:8080` (Vue CLI default)
- `http://localhost:4200` (Angular default)

If you're running on a different port, add it to the CORS configuration in `src/main.ts` and `src/chat/chat.gateway.ts`.

## 📋 Features

- **Real-time messaging** with WebSocket + HTTP fallback
- **Direct and group chats** with participant management
- **Optimized pagination** and **read receipts**
- **Rate limiting** (10 msg/min WebSocket, 30/min HTTP)
- **Input sanitization** and **typing indicators**
- **Connection health monitoring** with heartbeat
- **Memory leak prevention** and **performance optimized**

## 🔗 HTTP API Endpoints

All endpoints require `Authorization: Bearer <token>` header.

### Core Endpoints

#### Create Chat
```http
POST /api/chats
{
  "type": "direct",           // "direct" or "group"
  "participantIds": [2],      // User IDs (excluding yourself)
  "name": "Chat Name",        // Required for groups
  "description": "Optional"
}
```

#### Get User Chats
```http
GET /api/chats?limit=20&cursor=uuid
```

#### Get Messages
```http
GET /api/chats/:chatId/messages?limit=50&cursor=uuid
```

#### Send Message
```http
POST /api/chats/:chatId/messages
{
  "content": "Hello!",
  "type": "text"
}
```

#### Mark as Read
```http
PUT /api/chats/:chatId/read
```

### Response Format
All responses include proper error handling with status codes:
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Rate Limited

## 🔌 WebSocket Events

### Connection
```javascript
const socket = io('/chat', {
  auth: { token: 'your-jwt-token' }  // No 'Bearer' prefix
});

socket.on('connected', (data) => {
  console.log('Connected:', data);
});

socket.on('connect_error', (error) => {
  console.error('Connection failed:', error.message);
});
```

### Outgoing Events

#### Send Message
```javascript
socket.emit('send_message', {
  chatId: 'chat-uuid',
  message: {
    content: 'Hello!',
    type: 'text'
  }
});
```

#### Mark as Read
```javascript
socket.emit('mark_read', { chatId: 'chat-uuid' });
```

#### Typing Indicators
```javascript
socket.emit('typing_start', { chatId: 'chat-uuid' });
socket.emit('typing_stop', { chatId: 'chat-uuid' });
```

#### Health Check
```javascript
socket.emit('ping');
socket.on('pong', (data) => {
  console.log('Connection healthy:', data.timestamp);
});
```

### Incoming Events

#### New Message
```javascript
socket.on('new_message', (data) => {
  // data: { chatId, message: { id, content, sender, createdAt, isRead } }
});
```

#### Messages Read
```javascript
socket.on('messages_read', (data) => {
  // data: { chatId, userId, markedCount }
});
```

#### Typing Status
```javascript
socket.on('user_typing', (data) => {
  // data: { chatId, userId, username, isTyping }
});
```

### Rate Limits
- **WebSocket:** 10 messages/minute
- **HTTP:** 30 messages/minute

## 📊 TypeScript Interfaces

```typescript
// Core Types
export interface User {
  id: number;
  username: string;
  level: number;
}

export interface Chat {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  content: string;
  type: 'text' | 'system';
  sender: User;
  isRead: boolean;
  createdAt: string;
}

// API Responses
export interface PaginatedChatsResponse {
  chats: Chat[];
  hasMore: boolean;
  nextCursor?: string;
}

export interface PaginatedMessagesResponse {
  messages: Message[];
  hasMore: boolean;
  nextCursor?: string;
}

// WebSocket Events
export interface NewMessageEvent {
  chatId: string;
  message: Message;
}

export interface TypingEvent {
  chatId: string;
  userId: number;
  username: string;
  isTyping: boolean;
}
```

## 🔒 Authentication & Security

### JWT Token
```typescript
interface JWTPayload {
  userId: number;
  username: string;
  iat: number;
  exp: number;
}
```

### Security Features
- **JWT Validation** for all requests
- **Authorization** checks for chat access
- **Input Sanitization** with DOMPurify
- **Rate Limiting** to prevent spam
- **Input Validation** with size limits

### Validation Rules
- **Message**: 1-2000 characters
- **Chat Name**: 1-100 characters (groups)
- **Participants**: 1-50 users (groups)
- **Pagination**: 1-100 items per request

## 📱 Frontend Implementation

### React Hook Example

```typescript
import { useEffect, useState } from 'react';
import io from 'socket.io-client';

export function useChat(token: string) {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState({});

  useEffect(() => {
    const newSocket = io('/chat', { auth: { token } });

    newSocket.on('connect', () => setIsConnected(true));
    newSocket.on('disconnect', () => setIsConnected(false));

    newSocket.on('new_message', (data) => {
      setMessages(prev => ({
        ...prev,
        [data.chatId]: [...(prev[data.chatId] || []), data.message]
      }));
    });

    setSocket(newSocket);
    return () => newSocket.close();
  }, [token]);

  const sendMessage = (chatId, content) => {
    if (socket && isConnected) {
      socket.emit('send_message', {
        chatId,
        message: { content, type: 'text' }
      });
    }
  };

  return { socket, isConnected, messages, sendMessage };
}
```

### Vue 3 Example

```typescript
import { ref, onMounted, onUnmounted } from 'vue';
import io from 'socket.io-client';

export function useChat(token: string) {
  const socket = ref(null);
  const isConnected = ref(false);
  const messages = ref({});

  onMounted(() => {
    socket.value = io('/chat', { auth: { token } });

    socket.value.on('connect', () => {
      isConnected.value = true;
    });

    socket.value.on('new_message', (data) => {
      if (!messages.value[data.chatId]) {
        messages.value[data.chatId] = [];
      }
      messages.value[data.chatId].push(data.message);
    });
  });

  onUnmounted(() => {
    if (socket.value) {
      socket.value.close();
    }
  });

  return { socket, isConnected, messages };
}
```

### Vanilla JavaScript Example

```javascript
class ChatClient {
  constructor(token) {
    this.token = token;
    this.socket = null;
    this.isConnected = false;
    this.messages = {};
  }

  connect() {
    this.socket = io('/chat', { auth: { token: this.token } });

    this.socket.on('connect', () => {
      this.isConnected = true;
    });

    this.socket.on('new_message', (data) => {
      if (!this.messages[data.chatId]) {
        this.messages[data.chatId] = [];
      }
      this.messages[data.chatId].push(data.message);
    });
  }

  sendMessage(chatId, content) {
    if (this.socket && this.isConnected) {
      this.socket.emit('send_message', {
        chatId,
        message: { content, type: 'text' }
      });
    }
  }

  async loadChats() {
    const response = await fetch('/api/chats', {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    return response.json();
  }
}

// Usage
const chatClient = new ChatClient('your-jwt-token');
chatClient.connect();
```

## 🔧 Connection Management

### Automatic Reconnection

```javascript
const socket = io('/chat', {
  auth: { token },
  autoConnect: true,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 30000
});

socket.on('reconnect', (attemptNumber) => {
  console.log('Reconnected after', attemptNumber, 'attempts');
});

socket.on('reconnect_failed', () => {
  console.error('Failed to reconnect');
});
```

### Heartbeat Monitoring

```javascript
// Send ping every 30 seconds
setInterval(() => {
  if (socket.connected) {
    socket.emit('ping');
  }
}, 30000);

socket.on('pong', (data) => {
  console.log('Connection healthy:', data.timestamp);
});
```

## 🚨 Error Handling & Best Practices

### Error Handling

```javascript
// Handle WebSocket errors
socket.on('error', (error) => {
  console.error('WebSocket error:', error);
});

// Handle rate limiting
socket.on('connect_error', (error) => {
  if (error.message.includes('Rate limit')) {
    console.warn('Rate limited. Please slow down.');
  }
});

// Message validation
function validateMessage(content) {
  if (!content || content.trim().length === 0) {
    return { valid: false, error: 'Message cannot be empty' };
  }
  if (content.length > 2000) {
    return { valid: false, error: 'Message too long (max 2000 characters)' };
  }
  return { valid: true };
}

// HTTP fallback for failed WebSocket
async function sendMessageHTTP(chatId, content, token) {
  const response = await fetch(`/api/chats/${chatId}/messages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ content, type: 'text' })
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}
```

### Performance Tips

1. **Pagination**: Always implement message pagination
2. **Virtual Scrolling**: For large message lists
3. **Debounced Typing**: Reduce network traffic
4. **Message Caching**: Cache locally to reduce API calls
5. **Cleanup**: Remove event listeners on unmount

```javascript
// Debounced typing
const debouncedTyping = debounce((chatId, isTyping) => {
  if (isTyping) {
    socket.emit('typing_start', { chatId });
  } else {
    socket.emit('typing_stop', { chatId });
  }
}, 300);
```

## 📋 Production Checklist

### Before Going Live

- [ ] **Authentication**: Verify JWT token validation
- [ ] **Rate Limiting**: Test rate limiting behavior
- [ ] **Error Handling**: Implement comprehensive error handling
- [ ] **Reconnection**: Test automatic reconnection
- [ ] **Performance**: Test with large message histories
- [ ] **Security**: Verify input sanitization and authorization
- [ ] **Monitoring**: Set up WebSocket connection monitoring
- [ ] **Fallbacks**: Ensure HTTP fallbacks work
- [ ] **Database**: Run migration for performance indexes
- [ ] **Memory**: Verify no memory leaks
- [ ] **Heartbeat**: Test connection health monitoring

### Known Limitations

- **Message Editing**: Not supported (messages are immutable)
- **File Uploads**: Only text messages supported
- **Message Reactions**: Not implemented
- **Group Admin**: No admin/moderator roles
- **Message Search**: No search functionality
- **Push Notifications**: WebSocket only

### Troubleshooting

**Connection Issues:**
- Verify JWT token validity
- Check network connectivity
- Ensure WebSocket not blocked

**Message Not Sending:**
- Check rate limits (10 msg/min WebSocket)
- Verify chat participation
- Try HTTP fallback

**Debug Mode:**
```javascript
localStorage.debug = 'socket.io-client:socket';
```

---

This guide provides everything needed for frontend chat integration. The system is production-ready with all critical issues resolved and performance optimized.
